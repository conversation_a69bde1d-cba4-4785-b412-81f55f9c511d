"""
Flask Web Application for SQL Evaluator
Provides a user-friendly interface for SQL query generation and evaluation.
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import sys
import traceback
import time
from pathlib import Path
from datetime import datetime

# Add root directory to Python path for clean imports
sys.path.insert(0, str(Path(__file__).parent))

# Import our SQL evaluator components
from sql_judge_agent.judge import SQLJudge, SQLEvaluation
from sql_judge_agent.utils import get_db_schema
from llm_factory import create_llm
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from config_utils import load_db_config, load_full_config
from langfuse import Langfuse, observe
from langfuse.langchain import LangfuseTracer
from langchain.callbacks import tracing_v2_enabled

langfuse = Langfuse(
  secret_key="sk-lf-406b871e-262a-45c3-85ee-03aeddf82caa",
  public_key="pk-lf-7c27216e-943a-4d98-846a-3fdee25cde3e",
  host="https://cloud.langfuse.com"
)


app = Flask(__name__)
app.secret_key = 'sql-evaluator-secret-key-change-in-production'
CORS(app)

# Global variables to cache configuration and schema
config = None
db_schema = None
performer_llm = None
judge = None

def initialize_system():
    """Initialize the SQL evaluation system components."""
    global config, db_schema, performer_llm, judge
    
    try:
        # Load configuration
        config = load_full_config()
        print("✅ Configuration loaded from config.yaml")
        
        # Get database schema
        db_config = load_db_config()
        db_schema = get_db_schema(db_config)
        
        if not db_schema:
            raise Exception("Failed to fetch database schema")
        print("✅ Database schema loaded")
        
        # Initialize Performer LLM
        performer_config = config['performer_llm']
        performer_llm = create_llm(performer_config)
        print(f"✅ Performer LLM initialized: {performer_config.get('model_name', 'unknown')}")
        
        # Initialize Judge
        judge = SQLJudge(config['judge_llm'])
        print(f"✅ Judge LLM initialized: {config['judge_llm'].get('model_name', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        traceback.print_exc()
        return False

@app.route('/')
def index():
    """Main page with the SQL evaluation interface."""
    return render_template('index.html')
@observe()
@app.route('/api/evaluate', methods=['POST'])
def evaluate_sql():
    """API endpoint to evaluate a user prompt."""
    try:
        data = request.get_json()
        user_prompt = data.get('prompt', '').strip()
        
        if not user_prompt:
            return jsonify({
                'success': False,
                'error': 'Please provide a valid prompt'
            }), 400
        
        # Check if system is initialized
        if not all([config, db_schema, performer_llm, judge]):
            return jsonify({
                'success': False,
                'error': 'System not properly initialized. Please restart the application.'
            }), 500
        
        # Step 1: Generate SQL with Performer LLM
        start_time = time.time()
        
        performer_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an expert SQL writer. Given a database schema and a user question, write a single, syntactically correct SQL query to answer the question. Only output the SQL query."),
            ("human", "Schema:\n{schema}\n\nQuestion: {question}")
        ])
        
        performer_chain = performer_prompt | performer_llm | StrOutputParser()
        
        generated_sql = performer_chain.invoke({
            "schema": db_schema,
            "question": user_prompt
        })
        
        performer_time = time.time() - start_time
        
        # Step 2: Evaluate SQL with Judge LLM
        judge_start_time = time.time()
        
        evaluation_result = judge.evaluate(
            schema=db_schema,
            user_prompt=user_prompt,
            generated_sql=generated_sql
        )
        
        judge_time = time.time() - judge_start_time
        total_time = time.time() - start_time
        
        # Prepare response
        response = {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'user_prompt': user_prompt,
            'performer': {
                'model': config['performer_llm'].get('model_name', 'unknown'),
                'generated_sql': generated_sql.strip(),
                'execution_time': round(performer_time, 2)
            },
            'judge': {
                'model': config['judge_llm'].get('model_name', 'unknown'),
                'evaluation': evaluation_result.model_dump(),
                'execution_time': round(judge_time, 2)
            },
            'total_execution_time': round(total_time, 2)
        }
        
        return jsonify(response)
        
    except Exception as e:
        print(f"❌ Evaluation error: {e}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'Evaluation failed: {str(e)}'
        }), 500

@app.route('/api/system-info')
def system_info():
    """Get system configuration information."""
    try:
        if not config:
            return jsonify({
                'success': False,
                'error': 'System not initialized'
            }), 500
        
        info = {
            'success': True,
            'performer_model': config['performer_llm'].get('model_name', 'unknown'),
            'judge_model': config['judge_llm'].get('model_name', 'unknown'),
            'database_connected': db_schema is not None,
            'schema_tables_count': len(db_schema.split('CREATE TABLE')) - 1 if db_schema else 0
        }
        
        return jsonify(info)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sample-prompts')
def sample_prompts():
    """Get sample prompts for testing."""
    samples = [
        "Show me all users from the backend system",
        "Return the first 5 rows from the bkr users table",
        "Find all applications with their associated users",
        "Get the count of users by role",
        "List all workflow process instances that are completed",
        "Show me the most recent task instances",
        "Find users who have access to multiple applications"
    ]
    
    return jsonify({
        'success': True,
        'samples': samples
    })

if __name__ == '__main__':
    print("🚀 Starting SQL Evaluator Web Application...")
    
    # Initialize the system
    if initialize_system():
        print("✅ System initialized successfully!")
        print("🌐 Starting web server...")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to initialize system. Please check your configuration.")
        sys.exit(1)
